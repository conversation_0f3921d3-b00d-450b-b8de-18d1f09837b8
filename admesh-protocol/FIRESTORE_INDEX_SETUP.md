# Firestore Index Setup for AdMesh Protocol

## Quick Fix for History Feature

The ad generation history feature requires composite indexes in Firestore. Here's how to set them up:

## Option 1: Automatic Deployment (Recommended)

```bash
# From the admesh-protocol directory
./deploy-indexes.sh
```

## Option 2: Manual Setup via Firebase Console

### Required Index 1: Basic History Query
1. Go to [Firebase Console](https://console.firebase.google.com/project/admesh-dev/firestore/indexes)
2. Click "Create Index"
3. Set up the following:
   - **Collection ID**: `ad_generation_sessions`
   - **Fields**:
     - `brand_id` (Ascending)
     - `created_at` (Descending)
   - **Query scope**: Collection
4. Click "Create"

### Required Index 2: Filtered History Query
1. Click "Create Index" again
2. Set up the following:
   - **Collection ID**: `ad_generation_sessions`
   - **Fields**:
     - `brand_id` (Ascending)
     - `status` (Ascending)
     - `created_at` (Descending)
   - **Query scope**: Collection
3. Click "Create"

## Option 3: Direct Link (Fastest)

Click this link to create the main index automatically:
[Create Index](https://console.firebase.google.com/v1/r/project/admesh-dev/firestore/indexes?create_composite=Cllwcm9qZWN0cy9hZG1lc2gtZGV2L2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9hZF9nZW5lcmF0aW9uX3Nlc3Npb25zL2luZGV4ZXMvXxABGgwKCGJyYW5kX2lkEAEaDgoKY3JlYXRlZF9hdBACGgwKCF9fbmFtZV9fEAI)

## Verification

After creating the indexes:

1. Wait 2-5 minutes for the indexes to build
2. Refresh the ad generation page
3. Click the "History" button
4. You should now see your previous ad generations

## Fallback Behavior

The system includes fallback logic:
- If indexes don't exist, it will still work but may be slower
- History will be retrieved without optimal sorting
- A warning message will appear suggesting to create indexes

## Index Status

You can monitor index creation progress at:
[Firestore Indexes Console](https://console.firebase.google.com/project/admesh-dev/firestore/indexes)

## Troubleshooting

### Index Creation Failed
- Ensure you have Firebase Admin permissions
- Check that the project ID is correct (`admesh-dev`)
- Verify you're logged into the correct Google account

### History Still Not Working
1. Check browser console for errors
2. Verify the backend logs for any remaining issues
3. Try refreshing the page after index creation completes

### Performance Issues
- Indexes may take 5-10 minutes to fully optimize
- Large datasets may require additional time
- Consider limiting history queries if you have many sessions

## Additional Notes

- These indexes are required for production use
- Development environments should also have these indexes
- The indexes will improve query performance significantly
- No data migration is required - existing data will be automatically indexed
