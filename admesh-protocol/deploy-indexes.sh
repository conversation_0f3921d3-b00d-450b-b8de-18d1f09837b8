#!/bin/bash

# Deploy Firestore indexes for AdMesh Protocol
# This script deploys the required composite indexes for Firestore queries

echo "🔥 Deploying Firestore indexes for AdMesh Protocol..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase. Please login first:"
    echo "firebase login"
    exit 1
fi

# Deploy indexes
echo "📊 Deploying Firestore indexes..."
firebase deploy --only firestore:indexes

if [ $? -eq 0 ]; then
    echo "✅ Firestore indexes deployed successfully!"
    echo ""
    echo "📋 Deployed indexes for:"
    echo "  - ad_generation_sessions (brand_id + created_at)"
    echo "  - ad_generation_sessions (brand_id + status + created_at)"
    echo "  - sessions (agent_id + created_at)"
    echo "  - conversions (agent_id + timestamp)"
    echo "  - earnings (agent_id + timestamp)"
    echo "  - clicks (agent_id + timestamp)"
    echo ""
    echo "⏱️  Note: Index creation may take a few minutes to complete."
    echo "🔗 Monitor progress at: https://console.firebase.google.com/project/admesh-dev/firestore/indexes"
else
    echo "❌ Failed to deploy Firestore indexes"
    exit 1
fi
