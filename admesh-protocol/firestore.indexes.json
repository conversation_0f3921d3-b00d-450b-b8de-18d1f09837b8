{"indexes": [{"collectionGroup": "ad_generation_sessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "brand_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "ad_generation_sessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "brand_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "sessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agent_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "sessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agent_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "conversions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agent_id", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "conversions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agent_id", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "earnings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agent_id", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "earnings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agent_id", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "clicks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agent_id", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "clicks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agent_id", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}], "fieldOverrides": []}