"""
File Upload Service for AdMesh
Handles uploading visual assets (images, logos) to Firebase Storage
"""

import os
import logging
import uuid
import tempfile
from typing import Dict, Any, Optional, List
from firebase_admin import storage
from firebase.config import get_db
from config.config_manager import get_firebase_config
import mimetypes
from PIL import Image
import io

logger = logging.getLogger(__name__)

class FileUploadService:
    """Service for handling file uploads to Firebase Storage"""
    
    def __init__(self):
        # Get the storage bucket name from configuration
        firebase_config = get_firebase_config()
        bucket_name = firebase_config.get("storage_bucket")
        
        if not bucket_name:
            raise ValueError("Storage bucket name not found in Firebase configuration")
        
        self.bucket = storage.bucket(bucket_name)
        logger.info(f"✅ Initialized File Upload Service with bucket: {bucket_name}")
    
    def validate_image_file(self, file_content: bytes, max_size_mb: int = 5) -> Dict[str, Any]:
        """
        Validate image file content
        
        Args:
            file_content: Raw file bytes
            max_size_mb: Maximum file size in MB
            
        Returns:
            Dict with validation results
        """
        try:
            # Check file size
            file_size = len(file_content)
            max_size_bytes = max_size_mb * 1024 * 1024
            
            if file_size > max_size_bytes:
                return {
                    "valid": False,
                    "error": f"File size ({file_size / 1024 / 1024:.1f}MB) exceeds maximum allowed size ({max_size_mb}MB)"
                }
            
            # Try to open as image to validate format
            try:
                image = Image.open(io.BytesIO(file_content))
                image.verify()  # Verify it's a valid image
                
                # Get image dimensions
                image = Image.open(io.BytesIO(file_content))  # Reopen after verify
                width, height = image.size
                
                # Check minimum dimensions
                if width < 100 or height < 100:
                    return {
                        "valid": False,
                        "error": f"Image dimensions ({width}x{height}) are too small. Minimum 100x100 pixels required."
                    }
                
                # Check maximum dimensions
                if width > 4000 or height > 4000:
                    return {
                        "valid": False,
                        "error": f"Image dimensions ({width}x{height}) are too large. Maximum 4000x4000 pixels allowed."
                    }
                
                return {
                    "valid": True,
                    "width": width,
                    "height": height,
                    "format": image.format,
                    "size_bytes": file_size
                }
                
            except Exception as e:
                return {
                    "valid": False,
                    "error": f"Invalid image format: {str(e)}"
                }
                
        except Exception as e:
            return {
                "valid": False,
                "error": f"File validation error: {str(e)}"
            }
    
    async def upload_offer_image(
        self,
        brand_id: str,
        offer_id: str,
        file_content: bytes,
        filename: str,
        content_type: str = None
    ) -> Dict[str, Any]:
        """
        Upload an offer promotional image
        
        Args:
            brand_id: Brand's UID
            offer_id: Offer ID
            file_content: Raw file bytes
            filename: Original filename
            content_type: MIME type
            
        Returns:
            Dict with upload results
        """
        try:
            # Validate the image
            validation = self.validate_image_file(file_content)
            if not validation["valid"]:
                return {
                    "success": False,
                    "error": validation["error"]
                }
            
            # Generate unique filename
            file_extension = os.path.splitext(filename)[1].lower()
            if not file_extension:
                file_extension = ".jpg"  # Default extension
            
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            storage_path = f"brands/{brand_id}/offers/{offer_id}/images/{unique_filename}"
            
            # Determine content type
            if not content_type:
                content_type, _ = mimetypes.guess_type(filename)
                if not content_type:
                    content_type = "image/jpeg"
            
            # Create temporary file and upload
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                # Upload to Firebase Storage
                blob = self.bucket.blob(storage_path)
                blob.upload_from_filename(temp_file_path, content_type=content_type)
                
                # Make the file publicly readable
                blob.make_public()
                public_url = blob.public_url
                
                logger.info(f"Successfully uploaded offer image: {storage_path}")
                
                return {
                    "success": True,
                    "storage_path": storage_path,
                    "public_url": public_url,
                    "file_size": validation["size_bytes"],
                    "content_type": content_type,
                    "dimensions": {
                        "width": validation["width"],
                        "height": validation["height"]
                    },
                    "filename": unique_filename
                }
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            logger.error(f"Error uploading offer image: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def upload_product_logo(
        self,
        brand_id: str,
        product_id: str,
        file_content: bytes,
        filename: str,
        content_type: str = None
    ) -> Dict[str, Any]:
        """
        Upload a product logo
        
        Args:
            brand_id: Brand's UID
            product_id: Product ID
            file_content: Raw file bytes
            filename: Original filename
            content_type: MIME type
            
        Returns:
            Dict with upload results
        """
        try:
            # Validate the image
            validation = self.validate_image_file(file_content, max_size_mb=2)  # Smaller limit for logos
            if not validation["valid"]:
                return {
                    "success": False,
                    "error": validation["error"]
                }
            
            # Generate unique filename
            file_extension = os.path.splitext(filename)[1].lower()
            if not file_extension:
                file_extension = ".png"  # Default extension for logos
            
            unique_filename = f"logo_{uuid.uuid4()}{file_extension}"
            storage_path = f"brands/{brand_id}/products/{product_id}/logo/{unique_filename}"
            
            # Determine content type
            if not content_type:
                content_type, _ = mimetypes.guess_type(filename)
                if not content_type:
                    content_type = "image/png"
            
            # Create temporary file and upload
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                # Upload to Firebase Storage
                blob = self.bucket.blob(storage_path)
                blob.upload_from_filename(temp_file_path, content_type=content_type)
                
                # Make the file publicly readable
                blob.make_public()
                public_url = blob.public_url
                
                logger.info(f"Successfully uploaded product logo: {storage_path}")
                
                return {
                    "success": True,
                    "storage_path": storage_path,
                    "public_url": public_url,
                    "file_size": validation["size_bytes"],
                    "content_type": content_type,
                    "dimensions": {
                        "width": validation["width"],
                        "height": validation["height"]
                    },
                    "filename": unique_filename
                }
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            logger.error(f"Error uploading product logo: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def delete_file(self, storage_path: str) -> Dict[str, Any]:
        """
        Delete a file from Firebase Storage
        
        Args:
            storage_path: Path to the file in storage
            
        Returns:
            Dict with deletion results
        """
        try:
            blob = self.bucket.blob(storage_path)
            blob.delete()
            
            logger.info(f"Successfully deleted file: {storage_path}")
            
            return {
                "success": True,
                "message": f"File deleted: {storage_path}"
            }
            
        except Exception as e:
            logger.error(f"Error deleting file {storage_path}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
