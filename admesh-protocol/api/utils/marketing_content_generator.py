"""
Marketing Content Generator Service
Generates marketing-optimized titles and descriptions for offers using OpenAI
"""

import logging
import json
from typing import Dict, Any, Optional, List
from openai import OpenAI
import os

logger = logging.getLogger(__name__)

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

class MarketingContentGenerator:
    """Service for generating marketing-optimized content for offers"""
    
    @staticmethod
    def generate_marketing_content(
        product_title: str,
        product_description: str,
        categories: list = None,
        keywords: list = None,
        features: list = None,
        audience_segment: str = None,
        pricing_url: str = None
    ) -> Dict[str, Any]:
        """
        Generate marketing-optimized offer title and description
        
        Args:
            product_title: Original product title
            product_description: Original product description
            categories: Product categories
            keywords: Product keywords
            features: Product features
            audience_segment: Target audience
            pricing_url: Pricing page URL
            
        Returns:
            Dict containing offer_title, offer_description, and feature_sections
        """
        try:
            # Prepare context for the LLM
            context_parts = [
                f"Product: {product_title}",
                f"Description: {product_description}"
            ]
            
            if categories:
                context_parts.append(f"Categories: {', '.join(categories)}")
            
            if keywords:
                context_parts.append(f"Keywords: {', '.join(keywords[:5])}")  # Limit to top 5
                
            if features:
                context_parts.append(f"Key Features: {', '.join(features[:3])}")  # Limit to top 3
                
            if audience_segment:
                context_parts.append(f"Target Audience: {audience_segment}")
                
            context = "\n".join(context_parts)
            
            prompt = f"""
You are an expert marketing copywriter specializing in performance marketing and conversion optimization. 

Create compelling marketing content for this product that will be used in AI agent recommendations and ad formats.

Product Information:
{context}

Generate:
1. A marketing-focused offer title (max 60 characters) that highlights the key value proposition
2. A compelling offer description (max 150 characters) that emphasizes benefits and creates urgency
3. 3-5 key feature sections with titles and descriptions for expandable ad units

Requirements:
- Focus on benefits, not just features
- Use action-oriented language
- Highlight unique value propositions
- Make it conversion-focused
- Keep it concise and impactful
- Avoid generic phrases like "leading platform" or "cutting-edge"
- Feature sections should be specific and benefit-focused

Examples of good marketing titles:
- "Accept Global Payments with Stripe"
- "Build Apps 10x Faster with Supabase"
- "Automate Your Sales Pipeline with HubSpot"

Examples of good marketing descriptions:
- "Stripe makes it easy to accept payments online, in any currency, with built-in fraud protection."
- "Supabase gives you a Postgres database, authentication, and real-time subscriptions in minutes."
- "HubSpot automates your entire sales process from lead capture to deal closure."

Examples of good feature sections:
- {{"title": "Global Payment Processing", "description": "Accept payments from customers worldwide with support for 135+ currencies and local payment methods.", "icon": "💳"}}
- {{"title": "Real-time Analytics", "description": "Track your revenue, conversion rates, and customer behavior with detailed dashboards and reports.", "icon": "📊"}}
- {{"title": "Developer-Friendly APIs", "description": "Integrate quickly with comprehensive documentation, SDKs, and webhook support for all major platforms.", "icon": "⚡"}}

Return only valid JSON in this exact format:
{{
  "offer_title": "Your marketing title here",
  "offer_description": "Your compelling description here",
  "feature_sections": [
    {{"title": "Feature 1 Title", "description": "Benefit-focused description explaining how this feature helps users.", "icon": "🚀"}},
    {{"title": "Feature 2 Title", "description": "Benefit-focused description explaining how this feature helps users.", "icon": "💡"}},
    {{"title": "Feature 3 Title", "description": "Benefit-focused description explaining how this feature helps users.", "icon": "⚡"}}
  ]
}}
"""

            response = client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert marketing copywriter. Always respond with valid JSON only."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=300
            )
            
            content = response.choices[0].message.content.strip()
            
            # Parse the JSON response
            try:
                result = json.loads(content)
                
                # Validate the response has required fields
                if "offer_title" not in result or "offer_description" not in result:
                    raise ValueError("Missing required fields in LLM response")

                # Ensure length limits
                if len(result["offer_title"]) > 60:
                    result["offer_title"] = result["offer_title"][:57] + "..."

                if len(result["offer_description"]) > 150:
                    result["offer_description"] = result["offer_description"][:147] + "..."

                # Validate feature_sections
                if "feature_sections" not in result:
                    result["feature_sections"] = []

                # Ensure we have 3-5 feature sections
                feature_sections = result["feature_sections"]
                if len(feature_sections) < 3:
                    # Generate fallback features if not enough provided
                    fallback_features = MarketingContentGenerator._generate_fallback_features(
                        product_title, features or []
                    )
                    feature_sections.extend(fallback_features[:3 - len(feature_sections)])
                elif len(feature_sections) > 5:
                    feature_sections = feature_sections[:5]

                result["feature_sections"] = feature_sections

                logger.info(f"✅ Generated marketing content with {len(feature_sections)} features for: {product_title}")
                return result
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM response as JSON: {e}")
                # Fallback to basic marketing content
                return MarketingContentGenerator._generate_fallback_content(
                    product_title, product_description
                )
                
        except Exception as e:
            logger.error(f"Error generating marketing content: {str(e)}")
            # Return fallback content
            return MarketingContentGenerator._generate_fallback_content(
                product_title, product_description
            )
    
    @staticmethod
    def _generate_fallback_features(product_title: str, existing_features: list) -> List[Dict[str, str]]:
        """Generate fallback feature sections when LLM doesn't provide enough"""

        fallback_features = [
            {
                "title": f"{product_title} Core Features",
                "description": f"Discover the essential capabilities that make {product_title} powerful and easy to use.",
                "icon": "⚡"
            },
            {
                "title": "Easy Integration",
                "description": f"Get started with {product_title} quickly using our comprehensive documentation and support.",
                "icon": "🔧"
            },
            {
                "title": "Reliable Performance",
                "description": f"Count on {product_title} for consistent, high-quality results that scale with your needs.",
                "icon": "🚀"
            }
        ]

        return fallback_features

    @staticmethod
    def _generate_fallback_content(product_title: str, product_description: str) -> Dict[str, Any]:
        """Generate basic fallback marketing content when LLM fails"""

        # Create a simple marketing title
        if len(product_title) <= 60:
            offer_title = product_title
        else:
            offer_title = product_title[:57] + "..."

        # Create a basic description from the product description
        if len(product_description) <= 150:
            offer_description = product_description
        else:
            # Try to cut at a sentence boundary
            truncated = product_description[:147]
            last_period = truncated.rfind('.')
            if last_period > 100:  # Only use sentence boundary if it's not too short
                offer_description = truncated[:last_period + 1]
            else:
                offer_description = truncated + "..."

        # Generate fallback feature sections
        feature_sections = MarketingContentGenerator._generate_fallback_features(product_title, [])

        logger.info(f"Generated fallback marketing content for: {product_title}")

        return {
            "offer_title": offer_title,
            "offer_description": offer_description,
            "feature_sections": feature_sections
        }
