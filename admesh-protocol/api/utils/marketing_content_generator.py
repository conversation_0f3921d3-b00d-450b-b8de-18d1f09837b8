"""
Marketing Content Generator Service
Generates marketing-optimized titles and descriptions for offers using OpenAI
"""

import logging
import json
from typing import Dict, Any, Optional
from openai import OpenAI
import os

logger = logging.getLogger(__name__)

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

class MarketingContentGenerator:
    """Service for generating marketing-optimized content for offers"""
    
    @staticmethod
    def generate_marketing_content(
        product_title: str,
        product_description: str,
        categories: list = None,
        keywords: list = None,
        features: list = None,
        audience_segment: str = None,
        pricing_url: str = None
    ) -> Dict[str, str]:
        """
        Generate marketing-optimized offer title and description
        
        Args:
            product_title: Original product title
            product_description: Original product description
            categories: Product categories
            keywords: Product keywords
            features: Product features
            audience_segment: Target audience
            pricing_url: Pricing page URL
            
        Returns:
            Dict containing offer_title and offer_description
        """
        try:
            # Prepare context for the LLM
            context_parts = [
                f"Product: {product_title}",
                f"Description: {product_description}"
            ]
            
            if categories:
                context_parts.append(f"Categories: {', '.join(categories)}")
            
            if keywords:
                context_parts.append(f"Keywords: {', '.join(keywords[:5])}")  # Limit to top 5
                
            if features:
                context_parts.append(f"Key Features: {', '.join(features[:3])}")  # Limit to top 3
                
            if audience_segment:
                context_parts.append(f"Target Audience: {audience_segment}")
                
            context = "\n".join(context_parts)
            
            prompt = f"""
You are an expert marketing copywriter specializing in performance marketing and conversion optimization. 

Create compelling marketing content for this product that will be used in AI agent recommendations and ad formats.

Product Information:
{context}

Generate:
1. A marketing-focused offer title (max 60 characters) that highlights the key value proposition
2. A compelling offer description (max 150 characters) that emphasizes benefits and creates urgency

Requirements:
- Focus on benefits, not just features
- Use action-oriented language
- Highlight unique value propositions
- Make it conversion-focused
- Keep it concise and impactful
- Avoid generic phrases like "leading platform" or "cutting-edge"

Examples of good marketing titles:
- "Accept Global Payments with Stripe"
- "Build Apps 10x Faster with Supabase"
- "Automate Your Sales Pipeline with HubSpot"

Examples of good marketing descriptions:
- "Stripe makes it easy to accept payments online, in any currency, with built-in fraud protection."
- "Supabase gives you a Postgres database, authentication, and real-time subscriptions in minutes."
- "HubSpot automates your entire sales process from lead capture to deal closure."

Return only valid JSON in this exact format:
{{
  "offer_title": "Your marketing title here",
  "offer_description": "Your compelling description here"
}}
"""

            response = client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert marketing copywriter. Always respond with valid JSON only."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=300
            )
            
            content = response.choices[0].message.content.strip()
            
            # Parse the JSON response
            try:
                result = json.loads(content)
                
                # Validate the response has required fields
                if "offer_title" not in result or "offer_description" not in result:
                    raise ValueError("Missing required fields in LLM response")
                
                # Ensure length limits
                if len(result["offer_title"]) > 60:
                    result["offer_title"] = result["offer_title"][:57] + "..."
                    
                if len(result["offer_description"]) > 150:
                    result["offer_description"] = result["offer_description"][:147] + "..."
                
                logger.info(f"✅ Generated marketing content for: {product_title}")
                return result
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM response as JSON: {e}")
                # Fallback to basic marketing content
                return MarketingContentGenerator._generate_fallback_content(
                    product_title, product_description
                )
                
        except Exception as e:
            logger.error(f"Error generating marketing content: {str(e)}")
            # Return fallback content
            return MarketingContentGenerator._generate_fallback_content(
                product_title, product_description
            )
    
    @staticmethod
    def _generate_fallback_content(product_title: str, product_description: str) -> Dict[str, str]:
        """Generate basic fallback marketing content when LLM fails"""
        
        # Create a simple marketing title
        if len(product_title) <= 60:
            offer_title = product_title
        else:
            offer_title = product_title[:57] + "..."
        
        # Create a basic description from the product description
        if len(product_description) <= 150:
            offer_description = product_description
        else:
            # Try to cut at a sentence boundary
            truncated = product_description[:147]
            last_period = truncated.rfind('.')
            if last_period > 100:  # Only use sentence boundary if it's not too short
                offer_description = truncated[:last_period + 1]
            else:
                offer_description = truncated + "..."
        
        logger.info(f"Generated fallback marketing content for: {product_title}")
        
        return {
            "offer_title": offer_title,
            "offer_description": offer_description
        }
