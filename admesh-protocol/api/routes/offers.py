from fastapi import APIRouter, HTTPException, Request, Depends, Path
from pydantic import BaseModel, HttpUrl
from datetime import datetime
from firebase_admin import auth as firebase_auth
from firebase.config import get_db
from auth.deps import require_role
from google.cloud import firestore
from typing import Optional, Literal, List, Dict
import uuid
import logging

router = APIRouter()
db = get_db()
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PayoutModel(BaseModel):
    amount: int  # Amount in cents
    currency: str = "USD"
    model: Literal["CPA", "CPL", "CPI", "RevShare"]

class OfferIncentive(BaseModel):
    type: str  # "discount", "bonus", "free_trial", "credit", "extended_plan"
    headline: str
    details: str
    cta_label: str

class OfferImage(BaseModel):
    url: str
    storage_path: str
    filename: str
    content_type: str
    dimensions: Dict[str, int]  # {"width": 1200, "height": 800}

class FeatureSection(BaseModel):
    title: str
    description: str
    icon: str

class OfferModel(BaseModel):
    product_id: Optional[str] = None
    goal: Optional[Literal["signup", "purchase", "lead", "app_install", "click"]] = None
    payout: Optional[PayoutModel] = None

    reward_note: Optional[str] = None  # 💡 Fallback: "$X per goal"
    suggestion_reason: Optional[str] = None  # 🧠 Used for ranking or relevance notes

    offer_trust_score: Optional[float] = 50.0  # Renamed from trust_score
    trust_score: Optional[float] = 50.0  # Kept for backward compatibility
    valid_until: Optional[datetime] = None
    active: Optional[bool] = False  # Set offers as inactive by default
    offer_incentive: Optional[OfferIncentive] = None  # Optional offer incentive

    # New marketing content fields
    offer_title: Optional[str] = None  # Auto-generated marketing title
    offer_description: Optional[str] = None  # Auto-generated marketing description
    offer_images: Optional[List[OfferImage]] = []  # Promotional images
    feature_sections: Optional[List[FeatureSection]] = []  # Feature sections for expandable units

    # Budget tracking fields
    offer_total_budget_allocated: Optional[float] = None  # Total budget allocated for the offer
    offer_total_budget_spent: Optional[float] = None  # Budget spent for the offer
    offer_total_promo_spent: Optional[float] = None  # Promo credit spent for the offer
    offer_total_promo_available: Optional[float] = None  # Available promo credit for the offer

class IntegrationModel(BaseModel):
    method: Literal["redirect_pixel", "server_api", "manual"]
    webhook_url: Optional[HttpUrl] = None
    notes: Optional[str] = None
    redirect_url: Optional[str] = None
    target_urls: Optional[list[str]] = []

# Extended offer model for API requests that includes additional fields
class Offer(OfferModel):
    title: str
    description: str
    url: HttpUrl
    categories: list[str]
    keywords: list[str] = []

    meta: dict = {}

class OfferUpdate(BaseModel):
    product_id: Optional[str] = None
    goal: Optional[Literal["signup", "purchase", "lead", "app_install", "click"]] = None
    payout: Optional[PayoutModel] = None
    reward_note: Optional[str] = None
    suggestion_reason: Optional[str] = None
    offer_trust_score: Optional[float] = None  # Renamed from trust_score
    trust_score: Optional[float] = None  # Kept for backward compatibility
    valid_until: Optional[datetime] = None
    active: Optional[bool] = None
    offer_incentive: Optional[OfferIncentive] = None  # Optional offer incentive

    # Budget tracking fields
    promo_applied: Optional[bool] = None

    # New budget tracking fields
    offer_total_budget_allocated: Optional[float] = None  # Total budget allocated for the offer
    offer_total_budget_spent: Optional[float] = None  # Budget spent for the offer
    offer_total_promo_spent: Optional[float] = None  # Promo credit spent for the offer
    offer_total_promo_available: Optional[float] = None  # Available promo credit for the offer

    # Additional fields
    title: Optional[str] = None
    description: Optional[str] = None
    url: Optional[HttpUrl] = None
    categories: Optional[list[str]] = None
    keywords: Optional[list[str]] = None




@router.post("/register")
async def register_offer(request: Request, offer: Offer, user=Depends(require_role("brand"))):
    # Removed subscription limits functionality

    id_token = request.headers.get("Authorization", "").replace("Bearer ", "")
    try:
        decoded_token = firebase_auth.verify_id_token(id_token)
        brand_id = decoded_token["uid"]
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid Firebase token")

    # Validate product_id is provided
    if not offer.product_id:
        raise HTTPException(status_code=400, detail="product_id is required")

    # Removed subscription limit checks for keywords and offers

    product_ref = db.collection("products").document(offer.product_id)
    product_doc = product_ref.get()
    if not product_doc.exists:
        raise HTTPException(status_code=404, detail="Product not found")

    # Get product data for marketing content generation
    product_data = product_doc.to_dict()

    # Generate fallback reward_note if not provided
    if not offer.reward_note and offer.payout:
        goal_text = offer.goal or "conversion"
        # Convert cents to dollars for display
        amount_dollars = offer.payout.amount / 100
        offer.reward_note = f"${amount_dollars:.2f} per {goal_text}"

    offer_id = str(uuid.uuid4())
    offer_data = {
        **offer.model_dump(mode="json"),
        "brand_id": brand_id,
        "offer_id": offer_id,
        "active": offer.active if offer.active is not None else False,  # Set offers as inactive by default
    }

    # Auto-generate marketing content if not provided (fallback for cases where /intel endpoint wasn't used)
    if not offer_data.get("offer_title") or not offer_data.get("offer_description"):
        try:
            from api.utils.marketing_content_generator import MarketingContentGenerator

            marketing_content = MarketingContentGenerator.generate_marketing_content(
                product_title=product_data["title"],
                product_description=product_data["description"],
                categories=product_data.get("categories", []),
                keywords=product_data.get("keywords", []),
                audience_segment=product_data.get("audience_segment")
            )

            # Use generated content if not provided in payload
            if not offer_data.get("offer_title"):
                offer_data["offer_title"] = marketing_content["offer_title"]
            if not offer_data.get("offer_description"):
                offer_data["offer_description"] = marketing_content["offer_description"]
            if not offer_data.get("feature_sections"):
                offer_data["feature_sections"] = marketing_content.get("feature_sections", [])

            logger.info(f"✅ Auto-generated marketing content with {len(marketing_content.get('feature_sections', []))} features for offer {offer_id}")

        except Exception as e:
            logger.error(f"Failed to generate marketing content: {str(e)}")
            # Continue without auto-generated content

    offer_data.update({
        "conversion_count": {
            "total": 0,
            "production": 0,
            "test": 0
        },
        "click_count": {
            "total": 0,
            "production": 0,
            "test": 0
        },
        "offer_views": {                # Track views for the offer
            "test": 0,                  # Test views
            "production": 0,            # Production views
            "total": 0                  # Total views
        },
        "total_spent": {
            "production": 0.0,
            "test": 0.0,
            "all": 0.0
        },
        "total_budget_available": 0.0,  # Available budget for the offer
        "total_budget_spent": 0.0,      # Budget spent for the offer
        "total_promo_available": 0.0,   # Available promo credit for the offer
        "total_promo_spent": 0.0,       # Promo credit spent for the offer
        "offer_total_budget_allocated": 0.0,  # Total budget allocated for the offer
        "offer_total_budget_spent": 0.0,      # Budget spent for the offer
        "offer_total_promo_spent": 0.0,       # Promo credit spent for the offer
        "offer_total_promo_available": 0.0,   # Available promo credit for the offer
        "promo_applied": False,         # Flag to indicate if promo credit has been applied
        "offer_trust_score": 100,       # Set offer_trust_score to 100 for all new offers
        "created_at": firestore.SERVER_TIMESTAMP
    })

    db.collection("offers").document(offer_id).set(offer_data)
    db.collection("brands").document(brand_id).update({
        "active_offers": firestore.ArrayUnion([offer_id]),
        "active_products": firestore.ArrayUnion([offer.product_id]),
        "inactive_offers": firestore.ArrayRemove([offer_id]),
        "inactive_products": firestore.ArrayRemove([offer.product_id]),
    })
    product_ref.update({
        "active_offers": firestore.ArrayUnion([offer_id]),
        "inactive_offers": firestore.ArrayRemove([offer_id]),
    })

    # Removed subscription usage tracking

    return {"status": "success", "offer_id": offer_id}

@router.patch("/{offer_id}")
async def update_offer(
    offer_id: str,
    update: OfferUpdate,
    user=Depends(require_role("brand"))
):
    # Removed subscription limits functionality

    offer_ref = db.collection("offers").document(offer_id)
    offer_doc = offer_ref.get()
    if not offer_doc.exists:
        raise HTTPException(status_code=404, detail="Offer not found")

    existing_offer = offer_doc.to_dict()
    brand_id = existing_offer["brand_id"]
    product_id = existing_offer["product_id"]
    update_data = update.model_dump(exclude_unset=True, mode="json")

    # Removed subscription limit checks for keywords and offers

    offer_ref.update(update_data)

    if "active" in update_data:
        brand_ref = db.collection("brands").document(brand_id)
        product_ref = db.collection("products").document(product_id)
        if update_data["active"]:
            brand_ref.update({
                "inactive_offers": firestore.ArrayRemove([offer_id]),
                "active_offers": firestore.ArrayUnion([offer_id])
            })
            product_ref.update({
                "inactive_offers": firestore.ArrayRemove([offer_id]),
                "active_offers": firestore.ArrayUnion([offer_id])
            })
        else:
            brand_ref.update({
                "active_offers": firestore.ArrayRemove([offer_id]),
                "inactive_offers": firestore.ArrayUnion([offer_id])
            })
            product_ref.update({
                "active_offers": firestore.ArrayRemove([offer_id]),
                "inactive_offers": firestore.ArrayUnion([offer_id])
            })

        # Removed subscription usage tracking

    return {"status": "success", "message": "Offer updated"}

@router.post("/{offer_id}/apply-promo")
async def apply_promo_credit(
    offer_id: str,
    user=Depends(require_role("brand"))
):
    """
    Apply promotional credit to an offer

    This endpoint applies available promotional credit from the brand's wallet to the offer.
    The promo credit will be used for conversions before regular budget.

    Args:
        offer_id: The ID of the offer to apply promo credit to

    Returns:
        Success message with updated offer details

    Raises:
        HTTPException: If the offer is not found or the user is not authorized
    """
    # Get brand_id from authenticated user
    brand_id = user["uid"]

    # Get the offer document
    offer_ref = db.collection("offers").document(offer_id)
    offer_doc = offer_ref.get()

    if not offer_doc.exists:
        raise HTTPException(status_code=404, detail="Offer not found")

    offer_data = offer_doc.to_dict()

    # Check if the offer belongs to the authenticated brand
    if offer_data.get("brand_id") != brand_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this offer")

    # Check if promo credit has already been applied
    if offer_data.get("promo_applied", False):
        return {
            "status": "success",
            "message": "Promo credit has already been applied to this offer",
            "offer_id": offer_id,
            "promo_applied": True,
            "total_promo_available": offer_data.get("total_promo_available", 0.0)
        }

    # Get the brand's wallet
    wallet_ref = db.collection("wallets").document(brand_id)
    wallet_doc = wallet_ref.get()

    if not wallet_doc.exists:
        raise HTTPException(status_code=404, detail="Brand wallet not found")

    wallet_data = wallet_doc.to_dict()

    # Check if there's promo credit available
    promo_available = wallet_data.get("total_promo_available_balance", 0.0)

    if promo_available <= 0:
        return {
            "status": "error",
            "message": "No promotional credit available in your wallet",
            "offer_id": offer_id,
            "promo_applied": False
        }

    # Get the offer budget
    offer_budget = offer_data.get("offer_total_budget_allocated", 0.0)

    # Calculate the amount of promo credit to apply (up to the offer budget)
    promo_to_apply = min(promo_available, offer_budget)

    # Update the offer with promo credit
    offer_ref.update({
        "promo_applied": True,
        "total_promo_available": promo_to_apply,
        "offer_total_promo_available": promo_to_apply,
        "updated_at": firestore.SERVER_TIMESTAMP
    })

    # Update the wallet
    wallet_ref.update({
        "total_promo_available_balance": firestore.Increment(-promo_to_apply),
        "total_budget_allocated": firestore.Increment(promo_to_apply),
        "updated_at": firestore.SERVER_TIMESTAMP
    })

    return {
        "status": "success",
        "message": f"Successfully applied ${promo_to_apply/100:.2f} in promotional credit to this offer",
        "offer_id": offer_id,
        "promo_applied": True,
        "total_promo_available": promo_to_apply
    }

@router.delete("/{offer_id}")
async def delete_offer(offer_id: str, user=Depends(require_role("brand"))):
    # Removed subscription limits functionality

    # Get the offer document
    offer_ref = db.collection("offers").document(offer_id)
    offer_doc = offer_ref.get()

    if not offer_doc.exists:
        raise HTTPException(status_code=404, detail="Offer not found")

    # Get offer data
    offer_data = offer_doc.to_dict()
    brand_id = offer_data.get("brand_id")
    product_id = offer_data.get("product_id")
    was_active = offer_data.get("active", False)

    # Security check: ensure the user can only delete their own offers
    if brand_id != user["uid"]:
        logger.warning(f"Unauthorized delete attempt: User {user['uid']} tried to delete offer {offer_id} owned by {brand_id}")
        raise HTTPException(status_code=403, detail="You can only delete your own offers")

    # Check if offer has active conversions
    conversion_count_data = offer_data.get("conversion_count", 0)

    # Handle both old integer format and new dictionary format
    if isinstance(conversion_count_data, dict):
        conversion_count = conversion_count_data.get("total", 0)
    else:
        conversion_count = conversion_count_data or 0

    if conversion_count > 0:
        logger.info(f"Offer {offer_id} has {conversion_count} conversions but will be deleted anyway")

    # Delete the offer
    offer_ref.delete()
    logger.info(f"Offer deleted: {offer_id} by brand {brand_id}")

    # Update brand and product references
    if brand_id:
        brand_ref = db.collection("brands").document(brand_id)
        brand_ref.update({
            "active_offers": firestore.ArrayRemove([offer_id]),
            "inactive_offers": firestore.ArrayRemove([offer_id])
        })

    if product_id:
        product_ref = db.collection("products").document(product_id)
        product_ref.update({
            "active_offers": firestore.ArrayRemove([offer_id]),
            "inactive_offers": firestore.ArrayRemove([offer_id])
        })

        # If the offer was active, update the active offer count in the subscription
        # Removed subscription usage tracking

    return {"status": "success", "message": "Offer deleted successfully"}

@router.get("/get/{offer_id}")
async def get_offer_by_id(offer_id: str, user=Depends(require_role("brand"))):
    """
    Get a specific offer by ID

    This endpoint retrieves a single offer by its ID. The user must be authenticated
    and have the 'brand' role. The offer must belong to the authenticated brand.

    Args:
        offer_id: The ID of the offer to retrieve

    Returns:
        The offer data if found, with all fields properly formatted

    Raises:
        HTTPException: If the offer is not found or the user is not authorized
    """
    # Get brand_id from authenticated user
    brand_id = user["uid"]

    # Get the offer document
    offer_ref = db.collection("offers").document(offer_id)
    offer_doc = offer_ref.get()

    if not offer_doc.exists:
        raise HTTPException(status_code=404, detail="Offer not found")

    offer_data = offer_doc.to_dict()

    # Check if the offer belongs to the authenticated brand
    if offer_data.get("brand_id") != brand_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this offer")

    # Add the ID to the offer data
    offer_data["id"] = offer_id
    offer_data["offer_id"] = offer_id  # Add offer_id for consistency

    # Get product information if available
    product_id = offer_data.get("product_id")
    view_count = 0
    if product_id:
        product_doc = db.collection("products").document(product_id).get()
        if product_doc.exists:
            product_data = product_doc.to_dict()
            view_count = product_data.get("view_count", 0)
            offer_data["view_count"] = view_count

    # Get wallet information
    wallet_data = None
    if brand_id:
        wallet_ref = db.collection("wallets").document(brand_id)
        wallet_doc = wallet_ref.get()
        if wallet_doc.exists:
            wallet_data = wallet_doc.to_dict()
            offer_data["wallet"] = wallet_data
            print(f"DEBUG - Wallet data for brand {brand_id}: {wallet_data}")
        else:
            print(f"DEBUG - No wallet found for brand {brand_id}")
            # Create default wallet data
            default_wallet = {
                "brand_id": brand_id,
                "total_available_balance": 0.0,
                "total_promo_available_balance": 0.0,
                "total_promo_balance_spent": 0.0,
                "total_balance_spent": 0.0,
                "total_budget_allocated": 0.0
            }
            offer_data["wallet"] = default_wallet

    # Normalize clicks - extract both total and production clicks
    clicks_data = offer_data.get("click_count", 0)
    if isinstance(clicks_data, dict):
        clicks_total = clicks_data.get("total", 0)
        clicks_production = clicks_data.get("production", 0)
        clicks_test = clicks_data.get("test", 0)

        # Add explicit fields for easier access
        offer_data["clicks"] = clicks_total
        offer_data["clicks_production"] = clicks_production
        offer_data["clicks_test"] = clicks_test
    else:
        # Legacy format - assume all are production
        clicks_total = clicks_production = clicks_data
        clicks_test = 0

        # Add explicit fields for easier access
        offer_data["clicks"] = clicks_total
        offer_data["clicks_production"] = clicks_production
        offer_data["clicks_test"] = 0

        # Update click_count to use the new format
        offer_data["click_count"] = {
            "total": clicks_total,
            "production": clicks_production,
            "test": 0
        }

    # Normalize conversion counts - extract both total and production conversions
    conversion_data = offer_data.get("conversion_count", 0)
    if isinstance(conversion_data, dict):
        conversions_production = conversion_data.get("production", 0)
        conversions_test = conversion_data.get("test", 0)
        conversions_total = conversion_data.get("total", conversions_production + conversions_test)

        # Add explicit fields for easier access
        offer_data["conversions"] = conversions_production
        offer_data["conversions_production"] = conversions_production
        offer_data["conversions_test"] = conversions_test
        offer_data["conversions_total"] = conversions_total
    else:
        # Legacy format - assume all are production
        conversions_total = conversions_production = conversion_data
        conversions_test = 0

        # Add explicit fields for easier access
        offer_data["conversions"] = conversions_production
        offer_data["conversions_production"] = conversions_production
        offer_data["conversions_test"] = 0
        offer_data["conversions_total"] = conversions_total

        # Update conversion_count to use the new format
        offer_data["conversion_count"] = {
            "total": conversions_total,
            "production": conversions_production,
            "test": 0
        }

    # Normalize total spent
    spent_data = offer_data.get("total_spent", 0.0)
    if isinstance(spent_data, dict):
        production_spent = spent_data.get("production", 0.0)
        test_spent = spent_data.get("test", 0.0)
        total_spent = production_spent + test_spent

        # Add explicit field for easier access
        offer_data["total_spent_offer"] = production_spent
        offer_data["spent"] = production_spent
    else:
        # Legacy format - assume all is production
        production_spent = spent_data
        test_spent = 0.0
        total_spent = production_spent

        # Add explicit field for easier access
        offer_data["total_spent_offer"] = production_spent
        offer_data["spent"] = production_spent

        # Update total_spent to use the new format
        offer_data["total_spent"] = {
            "production": production_spent,
            "test": 0.0,
            "all": production_spent
        }

    # Calculate CTR using only production clicks and conversions (excluding test clicks and conversions)
    if clicks_production > 0:
        ctr = round((conversions_production / clicks_production * 100), 1)
    else:
        ctr = 0
    offer_data["ctr"] = ctr

    # Ensure tracking information is present
    if "tracking" not in offer_data:
        offer_data["tracking"] = {
            "method": "redirect_pixel",
            "redirect_url": "",
            "target_urls": []
        }

    # Ensure payout information is properly formatted
    if "payout" in offer_data and offer_data["payout"]:
        if "model" not in offer_data["payout"]:
            offer_data["payout"]["model"] = "CPA"
    else:
        offer_data["payout"] = {
            "amount": 0,
            "currency": "USD",
            "model": "CPA"
        }

    # Ensure other required fields are present
    if "keywords" not in offer_data:
        offer_data["keywords"] = []
    if "categories" not in offer_data:
        offer_data["categories"] = []
    if "description" not in offer_data:
        offer_data["description"] = ""
    if "url" not in offer_data:
        offer_data["url"] = ""
    if "reward_note" not in offer_data:
        offer_data["reward_note"] = ""
    if "offer_trust_score" not in offer_data:
        offer_data["offer_trust_score"] = 50.0

    # For backward compatibility, also set trust_score if it doesn't exist
    if "trust_score" not in offer_data:
        offer_data["trust_score"] = offer_data["offer_trust_score"]
    if "active" not in offer_data:
        offer_data["active"] = False
    if "goal" not in offer_data:
        offer_data["goal"] = "signup"
    if "offer_total_budget_allocated" not in offer_data:
        offer_data["offer_total_budget_allocated"] = 0.0

    # Add status field for consistency
    offer_data["status"] = "Active" if offer_data.get("active", False) else "Inactive"

    return offer_data

@router.get("/brand/all")
async def get_all_brand_offers(request: Request, user=Depends(require_role("brand"))):
    print("Fetching offers for brand")
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        raise HTTPException(status_code=401, detail="Missing auth token")

    try:
        token = auth_header.replace("Bearer ", "")
        decoded = firebase_auth.verify_id_token(token)
        brand_id = decoded["uid"]
        print(f"Brand ID: {brand_id}")
    except Exception as e:
        logger.error(f"Invalid Firebase token: {e}")
        raise HTTPException(status_code=401, detail="Invalid Firebase token")

    offers_query = db.collection("offers").where("brand_id", "==", brand_id)
    offers_snapshot = offers_query.stream()

    offers = []
    offer_clicks_total = 0  # All clicks (test + production)
    offer_clicks_production = 0  # Only production clicks
    offer_conversions_production = 0  # Only production conversions
    offer_conversions_test = 0  # Only test conversions
    offer_conversions_total = 0  # All conversions (test + production)
    total_budget = 0.0
    total_spent = 0.0
    total_view_count = 0  # Total view count from products
    total_offer_views_production = 0  # Total production views from offers
    total_offer_views_test = 0  # Total test views from offers

    for doc in offers_snapshot:
        data = doc.to_dict()
        offer_id = doc.id
        product_id = data.get("product_id")

        # Get view_count from the product document
        view_count = 0
        if product_id:
            product_doc = db.collection("products").document(product_id).get()
            if product_doc.exists:
                product_data = product_doc.to_dict()
                view_count = product_data.get("view_count", 0)
                # Add to total view count
                total_view_count += view_count

        # Extract offer_views data
        offer_views_data = data.get("offer_views", {})
        offer_views_production = 0
        offer_views_test = 0
        offer_views_total = 0

        if isinstance(offer_views_data, dict):
            offer_views_production = offer_views_data.get("production", 0)
            offer_views_test = offer_views_data.get("test", 0)
            offer_views_total = offer_views_data.get("total", 0)

            # Add to total offer views
            total_offer_views_production += offer_views_production
            total_offer_views_test += offer_views_test

        # Normalize clicks - extract both total and production clicks
        clicks_data = data.get("click_count", 0)
        clicks_total = clicks_production = clicks_test = 0

        if isinstance(clicks_data, dict):
            clicks_total = clicks_data.get("total", 0)
            clicks_production = clicks_data.get("production", 0)
            clicks_test = clicks_data.get("test", 0)
        else:
            # Legacy format - assume all are production
            clicks_total = clicks_production = clicks_data

        # Normalize conversion counts - extract both total and production conversions
        conversion_data = data.get("conversion_count", 0)
        conversions_production = conversions_test = conversions_total = 0

        if isinstance(conversion_data, dict):
            conversions_production = conversion_data.get("production", 0)
            conversions_test = conversion_data.get("test", 0)
            conversions_total = conversion_data.get("total", conversions_production + conversions_test)
        else:
            # Legacy format - assume all are production
            conversions_total = conversions_production = conversion_data

        # Get budget from offer_total_budget_allocated
        budget = data.get("offer_total_budget_allocated", 0.0)

        # Normalize total spent
        spent_data = data.get("total_spent", 0.0)
        production_spent = test_spent = 0.0

        if isinstance(spent_data, dict):
            production_spent = spent_data.get("production", 0.0)
            test_spent = spent_data.get("test", 0.0)
            spent = production_spent
        else:
            # Legacy format - assume all is production
            spent = production_spent = spent_data

        # Accumulate totals for all offers
        offer_clicks_total += clicks_total
        offer_clicks_production += clicks_production
        offer_conversions_test += conversions_test
        offer_conversions_production += conversions_production
        offer_conversions_total += conversions_total
        total_budget += budget
        total_spent += production_spent

        # Create a comprehensive offer object with all fields
        offer_obj = {
            "id": offer_id,
            "offer_id": offer_id,  # Add offer_id for consistency
            "brand_id": data.get("brand_id", brand_id),
            "product_id": product_id,

            # Basic information
            "title": data.get("title", "Untitled"),
            "description": data.get("description", ""),
            "url": data.get("url", ""),
            "keywords": data.get("keywords", []),
            "categories": data.get("categories", []),
            "view_count": view_count,  # Add view_count from product

            # Click tracking
            "clicks": clicks_total,
            "clicks_production": clicks_production,
            "clicks_test": clicks_data.get("test", 0) if isinstance(clicks_data, dict) else 0,
            "click_count": {
                "total": clicks_total,
                "production": clicks_production,
                "test": clicks_data.get("test", 0) if isinstance(clicks_data, dict) else 0
            },

            # Conversion tracking
            "conversions": conversions_production,
            "conversions_total": conversions_total,
            "conversions_test": conversions_test,
            "conversions_production": conversions_production,
            "conversion_count": {
                "total": conversions_total,
                "production": conversions_production,
                "test": conversions_test
            },

            # Financial data
            "offer_total_budget_allocated": budget,
            "offer_total_budget_spent": data.get("offer_total_budget_spent", 0.0),
            "offer_total_promo_spent": data.get("offer_total_promo_spent", 0.0),
            "offer_total_promo_available": data.get("offer_total_promo_available", 0.0),
            "offer_intial_promo_balance": data.get("offer_intial_promo_balance", 0.0),
            "promo_conversions_left": data.get("promo_conversions_left", 0),
            "promo_applied": data.get("promo_applied", False),
            "total_spent_offer": production_spent,
            "total_spent": {
                "production": production_spent,
                "test": test_spent,
                "all": production_spent + test_spent
            },
            "spent": spent,

            # Metrics - calculate CTR using only production clicks and conversions (excluding test clicks and conversions)
            "ctr": round((conversions_production / clicks_production * 100), 1) if clicks_production > 0 else 0,

            # Status
            "status": "Active" if data.get("active", False) else "Inactive",
            "active": data.get("active", False),

            # Payout and reward
            "reward_note": data.get("reward_note") or (
                f"${data['payout']['amount'] / 100} {data['payout'].get('currency', 'USD')}"
                if data.get("payout") and data.get("payout").get("amount") else "—"
            ),
            "payout": data.get("payout", {
                "amount": 0,
                "currency": "USD",
                "model": "CPA"
            }),

            # Goal and trust score
            "goal": data.get("goal", "signup"),
            "trust_score": data.get("trust_score", 50.0),

            # Timestamps
            "created_at": data.get("created_at"),
            "updated_at": data.get("updated_at"),
            "last_converted_at": data.get("last_converted_at"),

            # Tracking information
            "tracking": data.get("tracking", {
                "method": "redirect_pixel",
                "redirect_url": "",
                "target_urls": []
            }),

            # Additional metadata
            "meta": data.get("meta", {}),
            "valid_until": data.get("valid_until"),
            "suggestion_reason": data.get("suggestion_reason")
        }

        # Add the offer to the list
        offers.append(offer_obj)

    brand_doc = db.collection("brands").document(brand_id).get()
    product_clicks = 0
    product_conversions_production = 0

    if brand_doc.exists:
        brand_data = brand_doc.to_dict()
        product_ids = brand_data.get("active_products", [])
        print(f"Product IDs: {product_ids}")

        for pid in product_ids:
            product_doc = db.collection("products").document(pid).get()
            if product_doc.exists:
                pdata = product_doc.to_dict()
                print(f"Product data: {pdata}")
                product_clicks += pdata.get("clicks", 0)
                product_conversions_production += pdata.get("conversions_production", pdata.get("conversions", 0))

    total_clicks = int(product_clicks) + int(offer_clicks_total)
    total_clicks_production = int(product_clicks) + int(offer_clicks_production)
    total_conversions_production = int(product_conversions_production) + int(offer_conversions_production)
    total_conversions_test = int(offer_conversions_test)
    total_conversions_all = total_conversions_production + total_conversions_test

    # Calculate overall CTR using only production clicks and conversions (excluding test clicks and conversions)
    overall_ctr = round((total_conversions_production / total_clicks_production * 100), 1) if total_clicks_production > 0 else 0

    return {
        "status": "success",
        "offers": offers,
        "totals": {
            # View counts - combine product views and offer views
            "view_count": total_view_count + total_offer_views_production,
            "offer_views": {
                "total": total_offer_views_production + total_offer_views_test,
                "production": total_offer_views_production,
                "test": total_offer_views_test
            },
            "product_views": total_view_count,

            # Click counts
            "clicks": total_clicks,
            "clicks_production": total_clicks_production,
            "clicks_test": offer_clicks_total - offer_clicks_production,
            "click_count": {
                "total": total_clicks,
                "production": total_clicks_production,
                "test": offer_clicks_total - offer_clicks_production
            },

            # Conversion counts
            "conversions": total_conversions_production,
            "conversions_test": total_conversions_test,
            "conversions_all": total_conversions_all,
            "conversions_production": total_conversions_production,
            "conversions_total": total_conversions_all,
            "conversion_count": {
                "total": total_conversions_all,
                "production": total_conversions_production,
                "test": total_conversions_test
            },

            # Financial data
            "offer_total_budget_allocated": total_budget,
            "offer_total_budget_spent": sum(offer.get("offer_total_budget_spent", 0.0) for offer in offers),
            "offer_total_promo_spent": sum(offer.get("offer_total_promo_spent", 0.0) for offer in offers),
            "offer_total_promo_available": sum(offer.get("offer_total_promo_available", 0.0) for offer in offers),
            "total_spent_offer": total_spent,
            "spent": total_spent,
            "total_spent": {
                "production": total_spent,
                "test": 0.0,
                "all": total_spent
            },

            # Metrics
            "ctr": overall_ctr,

            # Detailed stats
            "offer_stats": {
                "clicks_total": offer_clicks_total,
                "clicks_production": offer_clicks_production,
                "clicks_test": offer_clicks_total - offer_clicks_production,
                "conversions_total": offer_conversions_total,
                "conversions_production": offer_conversions_production,
                "conversions_test": offer_conversions_test
            },
            "product_stats": {
                "clicks": product_clicks,
                "conversions": product_conversions_production
            }
        }
    }

class TrackingSetup(BaseModel):
    offer_id: str
    product_id: str
    method: Literal["redirect_pixel", "server_api", "manual"]
    webhook_url: Optional[str] = None
    notes: Optional[str] = None
    redirect_url: Optional[str] = None  # For redirect_pixel method
    target_urls: Optional[list[str]] = []  # List of target URLs

@router.post("/tracking/setup")
async def setup_tracking(tracking_setup: TrackingSetup, user=Depends(require_role("brand"))):
    # Get brand_id from authenticated user
    brand_id = user["uid"]
    offer_id = tracking_setup.offer_id

    # Verify offer exists and belongs to this brand
    offer_ref = db.collection("offers").document(offer_id)
    offer_doc = offer_ref.get()

    if not offer_doc.exists:
        raise HTTPException(status_code=404, detail="Offer not found")

    offer_data = offer_doc.to_dict()
    if offer_data.get("brand_id") != brand_id:
        raise HTTPException(status_code=403, detail="Not authorized to modify this offer")

    # Create tracking data
    tracking_data = {
        "method": tracking_setup.method,
        "webhook_url": tracking_setup.webhook_url,
        "notes": tracking_setup.notes,
        "redirect_url": tracking_setup.redirect_url,
        "target_urls": tracking_setup.target_urls or [],
        "updated_at": firestore.SERVER_TIMESTAMP
    }

    # Create simplified tracking object
    tracking = {
        "method": tracking_data["method"],
        "redirect_url": tracking_data["redirect_url"] or "",
        "target_urls": tracking_data["target_urls"]  # Use provided target URLs
    }

    # Update offer with tracking data
    offer_ref.update({
        "tracking": tracking
    })

    return {
        "status": "success",
        "message": "Tracking setup complete",
        "tracking": tracking
    }

class TrackingUpdate(BaseModel):
    product_id: str
    tracking: dict  # Contains method, redirect_url, target_urls, and notes

@router.post("/{offer_id}/tracking/update")
async def update_tracking(
    offer_id: str,
    payload: TrackingUpdate,
    user=Depends(require_role("brand"))
):
    """Update tracking settings for an offer"""
    # Get brand_id from authenticated user
    brand_id = user["uid"]

    # Verify offer exists and belongs to this brand
    offer_ref = db.collection("offers").document(offer_id)
    offer_doc = offer_ref.get()

    if not offer_doc.exists:
        raise HTTPException(status_code=404, detail="Offer not found")

    offer_data = offer_doc.to_dict()
    if offer_data.get("brand_id") != brand_id:
        raise HTTPException(status_code=403, detail="Not authorized to modify this offer")

    # Extract tracking data
    tracking_data = payload.tracking
    tracking_data["updated_at"] = firestore.SERVER_TIMESTAMP

    # Create tracking object
    tracking = {
        "method": tracking_data.get("method", "redirect_pixel"),
        "redirect_url": tracking_data.get("redirect_url", ""),
        "target_urls": tracking_data.get("target_urls", [])  # Use provided target URLs or empty list
    }

    # Update offer with tracking data
    offer_ref.update({
        "tracking": tracking
    })

    return {
        "status": "success",
        "message": "Tracking updated successfully",
        "tracking": tracking
    }
