!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n;n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,n.immediate=e()}}(function(){return function e(n,t,o){function r(f,u){if(!t[f]){if(!n[f]){var a="function"==typeof require&&require;if(!u&&a)return a(f,!0);if(i)return i(f,!0);var d=new Error("Cannot find module '"+f+"'");throw d.code="MODULE_NOT_FOUND",d}var s=t[f]={exports:{}};n[f][0].call(s.exports,function(e){var t=n[f][1][e];return r(t?t:e)},s,s.exports,e,n,t,o)}return t[f].exports}for(var i="function"==typeof require&&require,f=0;f<o.length;f++)r(o[f]);return r}({1:[function(e,n,t){(function(e){"use strict";function t(){s=!0;for(var e,n,t=c.length;t;){for(n=c,c=[],e=-1;++e<t;)n[e]();t=c.length}s=!1}function o(e){1!==c.push(e)||s||r()}var r,i=e.MutationObserver||e.WebKitMutationObserver;if(i){var f=0,u=new i(t),a=e.document.createTextNode("");u.observe(a,{characterData:!0}),r=function(){a.data=f=++f%2}}else if(e.setImmediate||"undefined"==typeof e.MessageChannel)r="document"in e&&"onreadystatechange"in e.document.createElement("script")?function(){var n=e.document.createElement("script");n.onreadystatechange=function(){t(),n.onreadystatechange=null,n.parentNode.removeChild(n),n=null},e.document.documentElement.appendChild(n)}:function(){setTimeout(t,0)};else{var d=new e.MessageChannel;d.port1.onmessage=t,r=function(){d.port2.postMessage(0)}}var s,c=[];n.exports=o}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[1])(1)});
