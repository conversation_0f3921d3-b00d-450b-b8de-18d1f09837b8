// app/dashboard/brand/offers/[id]/preview/page.tsx
"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { ChevronLeft, Edit2, AlertCircle, Tag, Play, Pause } from "lucide-react";
import OfferActivationDialog from "@/components/OfferActivationDialog";
import { useAuth } from "@/hooks/use-auth";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";

// Define the Offer interface
interface Offer {
  title: string;
  description?: string;
  reward_note?: string;
  active: boolean;
  categories?: string[];
  keywords?: string[];
  budget?: number;
  total_spent?: number;
  payout?: {
    amount?: number;
    currency?: string;
    model?: string;
  };

  click_count?: number;
  conversion_count?: number;

  // Marketing content fields
  offer_title?: string;
  offer_description?: string;
  feature_sections?: Array<{
    title: string;
    description: string;
    icon: string;
  }>;
}

export default function PreviewOfferPage() {
  const { id } = useParams() as { id: string };
  const router = useRouter();
  const { user } = useAuth();
  const [offer, setOffer] = useState<Offer | null>(null);
  const [loading, setLoading] = useState(true);
  const [showActivationDialog, setShowActivationDialog] = useState(false);

  useEffect(() => {
    const fetchOffer = async () => {
      setLoading(true);
      try {
        const ref = doc(db, "offers", id);
        const snap = await getDoc(ref);
        if (snap.exists()) {
          setOffer(snap.data() as Offer); // Explicitly cast to Offer
        }
      } catch (error) {
        console.error("Error fetching offer:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchOffer();
  }, [id]);

  if (loading) {
    return (
      <div className="max-w-3xl mx-auto py-10 space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-6 w-16" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2 mt-2" />
          </CardHeader>
          <CardContent className="space-y-5">
            <div>
              <Skeleton className="h-4 w-24" />
              <div className="flex gap-2 mt-2">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-6 w-24" />
              </div>
            </div>
            <Separator />
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-6 w-20 mt-1" />
              </div>
              <div>
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-6 w-32 mt-1" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!offer) {
    return (
      <div className="max-w-3xl mx-auto py-10 space-y-6">
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-6 flex items-center gap-3">
            <AlertCircle className="h-5 w-5 text-yellow-500" />
            <p className="text-yellow-700">Offer not found or may have been deleted.</p>
          </CardContent>
        </Card>
        <Button variant="outline" onClick={() => router.push('/dashboard/brand/offers')}>
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back to Offers
        </Button>
      </div>
    );
  }

  return (
    <>
      <div className="max-w-3xl mx-auto py-10 px-4 sm:px-6 space-y-6">
      {/* Top bar with navigation and status */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={() => router.push(`/dashboard/brand/offers/${id}/edit`)}
          >
            <Edit2 className="h-4 w-4 mr-2" />
            Edit Offer
          </Button>
          <Button
            variant={offer.active ? "destructive" : "default"}
            size="sm"
            onClick={() => setShowActivationDialog(true)}
          >
            {offer.active ? (
              <>
                <Pause className="h-4 w-4 mr-2" />
                Pause Offer
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Activate Offer
              </>
            )}
          </Button>
        </div>
        <Badge
          variant="outline"
          className={
            offer.active
              ? "bg-green-50 text-green-700 border-green-200"
              : "bg-gray-50 text-gray-600 border-gray-200"
          }
        >
          {offer.active ? "Active" : "Paused"}
        </Badge>
      </div>

      {/* Main offer details card */}
      <Card className="shadow-sm border-gray-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-xl font-semibold">{offer.offer_title || offer.title}</CardTitle>
          <CardDescription className="text-muted-foreground mt-1">
            {offer.offer_description || offer.reward_note}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6 text-sm">
          {/* Marketing Content Section */}
          {(offer.offer_title || offer.offer_description || (offer.feature_sections && offer.feature_sections.length > 0)) && (
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-600">
              <div className="flex items-center space-x-2 mb-3">
                <div className="w-5 h-5 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✨</span>
                </div>
                <h3 className="font-medium text-gray-700 dark:text-gray-300">AI-Generated Marketing Content</h3>
              </div>

              <div className="space-y-3">
                {offer.offer_title && (
                  <div>
                    <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Marketing Title</p>
                    <p className="text-sm font-semibold text-gray-900 dark:text-gray-100">{offer.offer_title}</p>
                  </div>
                )}

                {offer.offer_description && (
                  <div>
                    <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Marketing Description</p>
                    <p className="text-sm text-gray-900 dark:text-gray-100">{offer.offer_description}</p>
                  </div>
                )}

                {offer.feature_sections && offer.feature_sections.length > 0 && (
                  <div>
                    <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">Feature Sections ({offer.feature_sections.length})</p>
                    <div className="grid gap-2">
                      {offer.feature_sections.slice(0, 3).map((feature, index) => (
                        <div key={index} className="flex items-start space-x-2 p-2 bg-white/70 dark:bg-gray-800/70 rounded">
                          <span className="text-sm">{feature.icon}</span>
                          <div className="flex-1 min-w-0">
                            <p className="text-xs font-medium text-gray-900 dark:text-gray-100">{feature.title}</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">{feature.description}</p>
                          </div>
                        </div>
                      ))}
                      {offer.feature_sections.length > 3 && (
                        <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                          +{offer.feature_sections.length - 3} more features
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Description if available */}
          {offer.description && (
            <div>
              <h3 className="font-medium text-gray-700 mb-1">Description</h3>
              <p className="text-gray-600">{offer.description}</p>
            </div>
          )}

          {/* Categories */}
          <div>
            <h3 className="font-medium text-gray-700 mb-2">Categories</h3>
            <div className="flex flex-wrap gap-2">
              {(offer.categories ?? []).length > 0 ? (
                (offer.categories ?? []).map((c: string, i: number) => (
                  <Badge key={i} variant="outline" className="bg-gray-50">
                    <Tag className="h-3 w-3 mr-1 text-gray-500" />
                    {c}
                  </Badge>
                ))
              ) : (
                <span className="text-gray-500 text-sm">No categories</span>
              )}
            </div>
          </div>

          {/* Keywords */}
          <div>
            <h3 className="font-medium text-gray-700 mb-2">Keywords</h3>
            <div className="flex flex-wrap gap-2">
              {offer.keywords && offer.keywords.length > 0 ? (
                offer.keywords.map((keyword: string, i: number) => (
                  <Badge key={i} variant="secondary" className="bg-gray-100 text-gray-700">
                    {keyword}
                  </Badge>
                ))
              ) : (
                <span className="text-gray-500 text-sm">No keywords</span>
              )}
            </div>
          </div>

          <Separator className="my-2" />

          {/* Budget and Payout */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-700 mb-1">Budget</h3>
              <p className="text-lg font-semibold text-gray-900">${offer.budget?.toFixed(2) ?? "0.00"}</p>
              {offer.total_spent && (
                <p className="text-sm text-gray-500 mt-1">
                  Spent: ${offer.total_spent?.toFixed(2)}
                  ({Math.round((offer.total_spent / (offer.budget ?? 1)) * 100)}%)
                </p>
              )}
            </div>
            <div>
              <h3 className="font-medium text-gray-700 mb-1">Payout</h3>
              <p className="text-lg font-semibold text-gray-900">
                ${offer.payout?.amount} {offer.payout?.currency || "USD"}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Model: {offer.payout?.model || "CPA"}
              </p>
            </div>
          </div>

          {/* Performance metrics if available */}
          {((offer.click_count ?? 0) > 0 || (offer.conversion_count ?? 0) > 0) && (
            <>
              <Separator className="my-2" />
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                <div>
                  <h3 className="font-medium text-gray-700 mb-1">Clicks</h3>
                  <p className="text-lg font-semibold text-gray-900">{offer.click_count || 0}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-700 mb-1">Conversions</h3>
                  <p className="text-lg font-semibold text-gray-900">{offer.conversion_count || 0}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-700 mb-1">Conversion Rate</h3>
                  <p className="text-lg font-semibold text-gray-900">
                    {offer.click_count ? (((offer.conversion_count ?? 0) / offer.click_count) * 100).toFixed(1) : 0}%
                  </p>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>

    {/* Offer Activation Dialog */}
    {offer && (
      <OfferActivationDialog
        isOpen={showActivationDialog}
        onClose={() => {
          setShowActivationDialog(false);
          // Refresh the offer data after dialog closes
          if (user) {
            const fetchOffer = async () => {
              try {
                const ref = doc(db, "offers", id);
                const snap = await getDoc(ref);
                if (snap.exists()) {
                  setOffer(snap.data() as Offer);
                }
              } catch (error) {
                console.error("Error refreshing offer:", error);
              }
            };
            fetchOffer();
          }
        }}
        offerId={id}
        offerTitle={offer.title}
        offerBudget={offer.budget || 0}
        isActive={offer.active}
        offerMarketingTitle={offer.offer_title}
      />
    )}
    </>
  );
}