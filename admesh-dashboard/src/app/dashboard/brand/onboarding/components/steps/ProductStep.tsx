"use client";

import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, ArrowRight, Info } from "lucide-react";
import { KeywordInput } from "@/components/ui/tag-input";
import { MultiSelect } from "@/components/ui/multi-select";
import FormField from "../ui/FormField";
import FormSection from "../ui/FormSection";
import AnimatedContainer from "../ui/AnimatedContainer";
import { Product } from "@/types/onboarding";
import { PRODUCT_CATEGORIES, MAX_CATEGORIES_FREE_TIER } from "@/constants/categories";

interface ProductStepProps {
  product: {
    title: string;
    url: string;
    description: string;
    categories: string[];
    keywords: string[];
    pricing_url: string;
    audience_segment: string;
    integration_list: string[];
    active_offers: string[];
    inactive_offers: string[];
  };
  setProduct: (product: Product) => void;
  errors: Record<string, string>;
  onNext: () => void;
  onBack: () => void;
  loading: boolean;
  productExisted: boolean;
  plan?: { name: string; keyword_limit: number };
}

const ProductStep = ({
  product,
  setProduct,
  errors,
  onNext,
  onBack,
  loading,
  productExisted,
  plan = { name: 'Free', keyword_limit: 10 }
}: ProductStepProps) => {
  return (
    <AnimatedContainer dataStep={2}>
      <FormSection
        title="Tell us about your amazing product"
        description="Share the details that will help us showcase what makes your product special."
      >
        <div className="grid gap-6 w-full">
          <FormField>
            <Label htmlFor="title" className="text-sm font-medium">Product Title</Label>
            <Input
              id="title"
              placeholder="Your Amazing Product"
              value={product.title}
              readOnly
              className={`transition-all duration-200 bg-gray-50 dark:bg-gray-800 cursor-not-allowed ${errors.title ? 'border-red-500' : ''}`}
            />
            <div className="flex items-center space-x-1 mt-1">
              <Info className="w-3 h-3 text-blue-500" />
              <p className="text-xs text-blue-600 dark:text-blue-400">
                Product name automatically matches your brand name
              </p>
            </div>
            {errors.title && (
              <p className="text-xs text-red-500 mt-1">{errors.title}</p>
            )}
          </FormField>

          <FormField>
            <Label htmlFor="url" className="text-sm font-medium">Product URL</Label>
            <Input
              id="url"
              placeholder="https://yourbrand.com/product"
              value={product.url}
              onChange={(e) => setProduct({ ...product, url: e.target.value })}
              className={`transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.url ? 'border-red-500' : ''}`}
            />
            {errors.url && (
              <p className="text-xs text-red-500 mt-1">{errors.url}</p>
            )}
          </FormField>

          <FormField>
            <Label htmlFor="description" className="text-sm font-medium">Description</Label>
            <Textarea
              id="description"
              placeholder="Describe what your product does and its key benefits"
              rows={3}
              value={product.description}
              onChange={(e) => setProduct({ ...product, description: e.target.value })}
              className={`resize-none transition-all duration-200 focus:ring-2 focus:ring-primary/20 ${errors.description ? 'border-red-500' : ''}`}
            />
            {errors.description && (
              <p className="text-xs text-red-500 mt-1">{errors.description}</p>
            )}
          </FormField>

          <div className="grid grid-cols-1 gap-4">
            <FormField>
              <Label htmlFor="categories" className="text-sm font-medium">Categories</Label>
              <MultiSelect
                options={PRODUCT_CATEGORIES}
                value={product.categories}
                onChange={(categories) => setProduct({ ...product, categories })}
                placeholder="Select product categories..."
                maxSelections={plan?.name === 'Free' ? MAX_CATEGORIES_FREE_TIER : undefined}
                className={`transition-all duration-200 ${errors.categories ? 'border-red-500' : ''}`}
              />
              {errors.categories && (
                <p className="text-xs text-red-500 mt-1">{errors.categories}</p>
              )}
              {plan?.name === 'Free' && (
                <p className="text-xs text-muted-foreground mt-1">
                  Free plan users can select up to {MAX_CATEGORIES_FREE_TIER} categories
                </p>
              )}
            </FormField>

            <FormField>
              <Label htmlFor="keywords" className="text-sm font-medium">Keywords</Label>
              <KeywordInput
                value={product.keywords}
                onChange={(keywords) => setProduct({ ...product, keywords })}
                placeholder="Add keywords (e.g. productivity, tool, analytics)"
                className="transition-all duration-200"
                maxKeywords={plan?.keyword_limit || 10}
              />
              <div className="flex justify-between items-center mt-1">
                <p className="text-xs text-muted-foreground">
                  Press Enter or comma to add a keyword.
                </p>
                <div className="flex items-center gap-1">
                  <span className="text-xs font-medium">
                    {product.keywords.length}/{plan?.keyword_limit || 10}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    keywords ({plan?.name || 'Free'} plan)
                  </span>
                </div>
              </div>
            </FormField>
          </div>

          {/* <div className="grid grid-cols-1 gap-4">
            <FormField>
              <Label htmlFor="pricing_url" className="text-sm font-medium">Pricing URL</Label>
              <Input
                id="pricing_url"
                placeholder="https://yourbrand.com/pricing"
                value={product.pricing_url}
                onChange={(e) => setProduct({ ...product, pricing_url: e.target.value })}
                className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
              />
              <p className="text-xs text-muted-foreground mt-1">
                URL to your pricing page (optional)
              </p>
            </FormField>
          </div> */}

          {/* Removed deprecated fields: has_free_tier, is_ai_powered */}

          {/* Product exists note */}
          {productExisted && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="mt-4 p-3 bg-muted/50 border border-muted rounded-md text-muted-foreground"
            >
              <div className="flex items-start">
                <Info className="w-4 h-4 text-muted-foreground mr-2 mt-0.5 flex-shrink-0" />
                <p className="text-xs">
                  Your product already exists in our system. Once you complete onboarding, you&apos;ll be able to see all analytics for this product.
                </p>
              </div>
            </motion.div>
          )}

          <div className="flex flex-col-reverse sm:flex-row justify-between gap-3 sm:gap-0 pt-4">
            <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="w-full sm:w-auto">
              <Button
                variant="outline"
                onClick={onBack}
                className="w-full sm:w-auto border-primary/20 hover:border-primary/40 dark:border-primary/30 dark:hover:border-primary/50 transition-all duration-300 rounded-full px-5"
              >
                <div className="flex items-center">
                  <ArrowLeft className="mr-2 w-4 h-4 transition-transform duration-300 group-hover:-translate-x-1" /> Back
                </div>
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="w-full sm:w-auto">
              <Button
                onClick={onNext}
                disabled={loading}
                className="w-full sm:w-auto px-6 py-2 rounded-full relative overflow-hidden group shadow-md hover:shadow-lg"
              >
                <div className="relative z-10 flex items-center justify-center">
                  Next
                  <ArrowRight className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-primary to-blue-600 dark:from-primary dark:to-blue-500 opacity-90 group-hover:opacity-100 transition-all duration-300"></div>
              </Button>
            </motion.div>
          </div>
        </div>
      </FormSection>
    </AnimatedContainer>
  );
};

export default ProductStep;
