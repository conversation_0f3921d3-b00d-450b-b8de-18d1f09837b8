// lib/firebase-utils.ts
import { User, deleteUser } from "firebase/auth";

/**
 * Safely deletes a Firebase user account
 * @param user - The Firebase user to delete
 * @returns Promise<boolean> - true if deletion was successful, false otherwise
 */
export async function cleanupFirebaseUser(user: User): Promise<boolean> {
  try {
    await deleteUser(user);
    console.log("Firebase user successfully deleted:", user.uid);
    return true;
  } catch (error) {
    console.error("Failed to delete Firebase user:", error);
    
    // Log specific error details for debugging
    if (error && typeof error === 'object' && 'code' in error) {
      console.error("Firebase deletion error code:", error.code);
      console.error("Firebase deletion error message:", error.message);
    }
    
    return false;
  }
}

/**
 * Handles cleanup of Firebase user when backend registration fails
 * This function is designed to be called in error handling scenarios
 * @param user - The Firebase user to clean up
 * @param originalError - The original error that caused the need for cleanup
 */
export async function handleRegistrationFailureCleanup(
  user: User | null, 
  originalError: Error
): Promise<void> {
  if (!user) {
    console.warn("No Firebase user to clean up");
    return;
  }

  console.log("Backend registration failed, attempting to clean up Firebase user:", user.uid);
  
  const cleanupSuccess = await cleanupFirebaseUser(user);
  
  if (!cleanupSuccess) {
    console.error(
      "CRITICAL: Firebase user cleanup failed. Manual cleanup may be required for user:",
      user.uid,
      "Original error:",
      originalError.message
    );
  }
}
