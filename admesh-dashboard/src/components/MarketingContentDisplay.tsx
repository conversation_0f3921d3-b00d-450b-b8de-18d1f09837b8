"use client";

import React from 'react';
import { Sparkles, RefreshCw } from 'lucide-react';

interface FeatureSection {
  title: string;
  description: string;
  icon: string;
}

interface MarketingContentDisplayProps {
  title?: string;
  description?: string;
  featureSections?: FeatureSection[];
  isGenerating?: boolean;
  onRegenerate?: () => void;
  className?: string;
}

export const MarketingContentDisplay: React.FC<MarketingContentDisplayProps> = ({
  title,
  description,
  featureSections = [],
  isGenerating = false,
  onRegenerate,
  className = ""
}) => {
  if (!title && !description && featureSections.length === 0 && !isGenerating) {
    return null;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Sparkles className="w-5 h-5 text-purple-500" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            AI-Generated Marketing Content
          </h3>
        </div>
        {onRegenerate && !isGenerating && (
          <button
            type="button"
            onClick={onRegenerate}
            className="flex items-center space-x-1 px-3 py-1 text-sm text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 border border-purple-200 dark:border-purple-600 rounded-md hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Regenerate</span>
          </button>
        )}
      </div>

      {isGenerating ? (
        <div className="space-y-4">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6 mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center space-x-2">
            <RefreshCw className="w-4 h-4 animate-spin" />
            <span>Generating marketing content...</span>
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {title && (
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-600">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Marketing Title
              </label>
              <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {title}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {title.length}/60 characters
              </p>
            </div>
          )}

          {description && (
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-600">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Marketing Description
              </label>
              <p className="text-gray-900 dark:text-gray-100 leading-relaxed">
                {description}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {description.length}/150 characters
              </p>
            </div>
          )}

          {featureSections.length > 0 && (
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-4 rounded-lg border border-green-200 dark:border-green-600">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Feature Sections ({featureSections.length})
              </label>
              <div className="space-y-3">
                {featureSections.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-green-100 dark:border-green-700">
                    <span className="text-lg flex-shrink-0">{feature.icon}</span>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-1">
                        {feature.title}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              <strong>How it works:</strong> This content is automatically optimized for AI agent recommendations and ad formats.
              The marketing title will be used as the primary headline, the description will appear in inline ads, and feature sections will populate expandable ad units.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};
