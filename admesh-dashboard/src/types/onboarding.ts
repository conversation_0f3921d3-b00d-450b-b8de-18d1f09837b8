// Type definitions for onboarding components

// Original onboarding types
export interface Brand {
  website: string;
  brand_name: string;
  logo_url: string;
  work_email: string;
  headquarters: string;
  application_type: string;
}

export interface Product {
  title: string;
  url: string;
  description: string;
  categories: string[]; // Changed from single category to multiple categories
  keywords: string[];
  pricing_url: string;
  audience_segment: string;
  integration_list: string[];
  active_offers: string[];
  inactive_offers: string[];
  // New visual asset field
  product_logo?: ProductLogo; // Optional product logo
}

export interface OfferIncentive {
  type: "discount" | "bonus" | "free_trial" | "credit" | "extended_plan";
  headline: string;
  details: string;
  cta_label: string;
}

export interface OfferImage {
  url: string;
  storage_path: string;
  filename: string;
  content_type: string;
  dimensions: {
    width: number;
    height: number;
  };
}

export interface ProductLogo {
  url: string;
  storage_path: string;
  filename: string;
  content_type: string;
  dimensions: {
    width: number;
    height: number;
  };
}

export interface Offer {
  goal: string;
  model: string;
  payout_amount: string;
  budget: string;
  promo_applied: boolean; // Flag to track if promo credit has been applied
  offer_incentive?: OfferIncentive; // Optional offer incentive
  // New marketing content fields
  offer_title?: string; // Auto-generated marketing title
  offer_description?: string; // Auto-generated marketing description
  offer_images?: OfferImage[]; // Promotional images
}

export interface Tracking {
  method: string;
  webhook_url: string;
  notes: string;
  redirect_url: string;
  target_urls: string[];
}

// New onboarding component types
export interface BasicInfoStepProps {
  agentName: string;
  setAgentName: (value: string) => void;
  agentType: string;
  setAgentType: (value: string) => void;
  otherAgentType: string;
  setOtherAgentType: (value: string) => void;
  agentUrl: string;
  setAgentUrl: (value: string) => void;
  userCount: string;
  setUserCount: (value: string) => void;
  onNext: () => void;
  isLoading: boolean;
}

// API Key information
export interface ApiKeyInfo {
  key: string;
  id: string;
}

export interface ApiCredentialsStepProps {
  agentId: string;
  testApiKey: string;
  productionApiKey: string;
  onComplete: () => void;
  isLoading: boolean;
  // Optional callbacks for updating API keys
  onTestKeyGenerated?: (keyInfo: ApiKeyInfo) => void;
  onProductionKeyGenerated?: (keyInfo: ApiKeyInfo) => void;
}

// AdMesh Recommendation interface for component previews
export interface AdMeshRecommendation {
  title: string;
  reason: string;
  intent_match_score: number;
  admesh_link: string;
  ad_id: string;
  product_id: string;
  features?: string[];
  has_free_tier?: boolean;
  pricing?: string;
  trial_days?: number;
  keywords?: string[];
}

export interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
}

export interface TestApiResponse {
  [key: string]: unknown;
}

// Agent Types
export const AGENT_TYPES = [
  "Chatbot",
  "GPT Plugin",
  "Chrome Extension",
  "SaaS App",
  "Other"
];

// User Count Options
export const USER_COUNT_OPTIONS = [
  "1-100",
  "100-1K",
  "1K-10K",
  "10K-100K",
  "100K-500K",
  "500K-1M",
  "1M+"
];
